#!/usr/bin/env python3
"""
Build script for Apply Scale Extension

This script creates a zip package that can be installed as a Blender extension.
"""

import os
import zipfile
import shutil
from pathlib import Path

def create_extension_package():
    """Create a zip package for the extension"""
    
    # Get the current directory
    current_dir = Path(__file__).parent
    
    # Extension files to include
    extension_files = [
        "blender_manifest.toml",
        "__init__.py",
        "README.md"
    ]
    
    # Output directory and filename
    output_dir = current_dir / "dist"
    output_dir.mkdir(exist_ok=True)
    
    zip_filename = output_dir / "apply_scale_extension.zip"
    
    # Create the zip file
    with zipfile.ZipFile(zip_filename, 'w', zipfile.ZIP_DEFLATED) as zipf:
        for file_path in extension_files:
            file_full_path = current_dir / file_path
            if file_full_path.exists():
                zipf.write(file_full_path, file_path)
                print(f"Added: {file_path}")
            else:
                print(f"Warning: {file_path} not found")
    
    print(f"\nExtension package created: {zip_filename}")
    print(f"Size: {zip_filename.stat().st_size / 1024:.1f} KB")
    
    return zip_filename

def validate_manifest():
    """Validate the manifest file"""
    manifest_path = Path(__file__).parent / "blender_manifest.toml"
    
    if not manifest_path.exists():
        print("Error: blender_manifest.toml not found!")
        return False
    
    try:
        import tomllib
    except ImportError:
        try:
            import tomli as tomllib
        except ImportError:
            print("Warning: Cannot validate TOML file (tomllib/tomli not available)")
            return True
    
    try:
        with open(manifest_path, 'rb') as f:
            manifest = tomllib.load(f)
        
        required_fields = ['schema_version', 'id', 'version', 'name', 'type']
        missing_fields = [field for field in required_fields if field not in manifest]
        
        if missing_fields:
            print(f"Error: Missing required fields in manifest: {missing_fields}")
            return False
        
        print("✓ Manifest validation passed")
        return True
        
    except Exception as e:
        print(f"Error validating manifest: {e}")
        return False

if __name__ == "__main__":
    print("Building Apply Scale Extension...")
    print("=" * 40)
    
    # Validate manifest
    if not validate_manifest():
        exit(1)
    
    # Create package
    package_path = create_extension_package()
    
    print("\n" + "=" * 40)
    print("Build completed successfully!")
    print(f"Package: {package_path}")
    print("\nTo install:")
    print("1. Open Blender 4.2+")
    print("2. Go to Edit > Preferences > Extensions")
    print("3. Click the dropdown next to + and select 'Install from Disk...'")
    print("4. Select the created zip file")
