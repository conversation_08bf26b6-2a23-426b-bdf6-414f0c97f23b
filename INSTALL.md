# Installation Guide

## For Blender 4.2+ (Extension Format)

### Option 1: Using Extension Manager (Recommended)
1. Download the entire folder or create a zip file containing:
   - `blender_manifest.toml`
   - `__init__.py`
   - `README.md`

2. Open Blender 4.2 or newer

3. Go to `Edit > Preferences > Extensions`

4. Click the dropdown arrow next to the `+` button

5. Select `Install from Disk...`

6. Choose the folder or zip file containing the extension

7. The extension will be automatically installed and enabled

### Option 2: Manual Installation
1. Locate your Blender extensions directory:
   - **Windows**: `%APPDATA%\Blender Foundation\Blender\4.2\extensions\user_default\`
   - **macOS**: `~/Library/Application Support/Blender/4.2/extensions/user_default/`
   - **Linux**: `~/.config/blender/4.2/extensions/user_default/`

2. Create a new folder named `apply_scale_addon` in the extensions directory

3. Copy these files into the new folder:
   - `blender_manifest.toml`
   - `__init__.py`
   - `README.md`

4. <PERSON>art Blender

5. The extension should appear in `Edit > Preferences > Extensions`

## For Blender 3.0 - 4.1 (Legacy Addon Format)

1. Use the `__init_addon.py` file (rename it to `__init__.py` if needed)

2. Open Blender

3. Go to `Edit > Preferences > Add-ons`

4. Click `Install...`

5. Select the `__init_addon.py` file

6. Enable the addon by checking the box next to "Apply Scale"

## Building Extension Package

If you want to create a distributable package:

1. Run the build script:
   ```bash
   python build_extension.py
   ```

2. This creates a `dist/apply_scale_extension.zip` file

3. This zip file can be installed directly via the Extension Manager

## Verification

After installation, verify the extension works:

1. Create a default cube in Blender
2. Scale it to something other than (1,1,1) using `S` key
3. Select the cube
4. You should see a blue button in the bottom-right corner of the 3D viewport
5. Click the button to apply the scale

## Troubleshooting

### Extension not appearing
- Make sure you're using Blender 4.2+
- Check that `blender_manifest.toml` is present and valid
- Restart Blender after manual installation

### Button not showing
- Make sure an object is selected
- Verify the object has non-uniform scale (not 1,1,1)
- Check that you're in the 3D viewport

### Legacy addon issues
- For Blender < 4.2, use `__init_addon.py`
- Make sure the file has the correct `bl_info` structure
- Check Blender console for error messages

## Uninstallation

### Extension (Blender 4.2+)
1. Go to `Edit > Preferences > Extensions`
2. Find "Apply Scale Addon" in the list
3. Click the remove button

### Legacy Addon
1. Go to `Edit > Preferences > Add-ons`
2. Find "Apply Scale" in the list
3. Uncheck the box to disable
4. Click the remove button to uninstall
